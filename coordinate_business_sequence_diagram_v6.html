<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coordinate 业务时序图 v6.0 - Cube FastAPI</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #ff7b7b 0%, #667eea 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 300;
        }
        
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e9ecef;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .mermaid {
            text-align: center;
            min-width: 1600px;
        }
        
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
            margin: 20px 0;
        }
        
        .business-highlights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .business-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .business-item h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .footer {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 40px;
        }
        
        .version-badge {
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
        
        .file-format-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
            margin: 20px 0;
        }
        
        .api-overview {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #dc3545;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📍 Coordinate 业务时序图 <span class="version-badge">v6.0</span></h1>
            <p>Cube FastAPI 坐标管理系统完整业务流程与技术架构分析</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 重要说明：</strong>此时序图展示了coordinate模块的5个核心业务功能：批量导入、查询坐标、删除坐标、更新坐标、查询关联词汇。每个业务都有完整的技术实现流程。图表内容较宽，建议使用横向滚动查看。
            </div>
            
            <div class="api-overview">
                <h3>🎯 Coordinate API 业务概览</h3>
                <ul>
                    <li><strong>GET /coordinate/batch</strong>：从cor.txt文件批量导入坐标数据</li>
                    <li><strong>GET /coordinate/find</strong>：查询指定表格的所有坐标数据</li>
                    <li><strong>DELETE /coordinate/delete</strong>：删除表格的所有坐标数据</li>
                    <li><strong>PUT /coordinate/update</strong>：更新单个坐标信息（含重复计数）</li>
                    <li><strong>GET /coordinate/list</strong>：查询坐标关联的词汇列表</li>
                </ul>
            </div>

            <div class="file-format-box">
                <h3>📄 cor.txt 文件格式说明</h3>
                <p><strong>格式：</strong>（x， y） color</p>
                <p><strong>示例：</strong></p>
                <pre>（10， 20） 0
（15， 25） 1
（-5， 30） 2</pre>
                <p><strong>解析规则：</strong>使用正则表达式 <code>r'（(-?\d+)，\s*(-?\d+)）\s+(\d+)'</code> 提取坐标和颜色信息</p>
            </div>

            <div class="info-box">
                <h3>🔗 数据关系说明</h3>
                <ul>
                    <li><strong>Table ↔ Coordinate</strong>：一对多关系，通过table_id关联</li>
                    <li><strong>Coordinate ↔ TextInfo</strong>：通过color字段间接关联</li>
                    <li><strong>TextInfo ↔ Phrase</strong>：一对多关系，通过text_id关联</li>
                    <li><strong>重复计数机制</strong>：相同voc+color的坐标自动计算repeated值</li>
                </ul>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
sequenceDiagram
    participant Client as 🖥️ 前端客户端
    participant FastAPI as 🌐 FastAPI路由器
    participant CoordinateService as 🟣 服务
    participant FileSystem as 📁 文件系统
    participant IdGenerator as 🆔 IdGenerator
    participant SQLAlchemy as 🟠 SQLAlchemy ORM
    participant Database as 🗄️ SQLite数据库

    Note over Client, Database: Coordinate 业务流程总览 v6.0

    %% 业务1：批量导入坐标
    rect rgb(255, 248, 220)
        Note over Client, Database: 🔄 业务1：批量导入坐标 (GET /coordinate/batch)
        
        Client->>FastAPI: GET /coordinate/batch?id=1<br/>批量导入表格1的坐标数据
        FastAPI->>CoordinateService: batch_import_coordinates(table_id=1)
        
        CoordinateService->>FileSystem: 读取项目根目录/cor.txt文件
        FileSystem->>FileSystem: 使用UTF-8编码读取文件内容
        FileSystem-->>CoordinateService: 文件内容字符串
        
        CoordinateService->>CoordinateService: 正则解析坐标格式<br/>pattern: r'（(-?\d+)，\s*(-?\d+)）\s+(\d+)'
        
        loop 遍历每行坐标数据
            CoordinateService->>CoordinateService: 解析：（10， 20） 0<br/>→ x=10, y=20, color=0
            CoordinateService->>IdGenerator: generate_id() 雪花算法
            IdGenerator-->>CoordinateService: unique_id
            CoordinateService->>CoordinateService: 创建Coordinate对象<br/>position="（10， 20）", voc="", repeated=0
        end
        
        CoordinateService->>SQLAlchemy: 分批插入坐标数据<br/>batch_size=1000, sleep_ms=100
        SQLAlchemy->>Database: INSERT INTO coordinate (id, table_id, color, position, voc, repeated) VALUES (...)
        Database-->>SQLAlchemy: 插入结果
        
        CoordinateService-->>FastAPI: {"coordinates": [...], "total": count}
        FastAPI-->>Client: HTTP 200 批量导入成功
    end

    %% 业务2：查询坐标数据
    rect rgb(240, 248, 255)
        Note over Client, Database: 🔍 业务2：查询坐标数据 (GET /coordinate/find)

        Client->>FastAPI: GET /coordinate/find?id=1<br/>查询表格1的所有坐标
        FastAPI->>CoordinateService: get_coordinates_by_table(table_id=1)

        CoordinateService->>SQLAlchemy: query(Coordinate).filter(table_id==1).all()
        SQLAlchemy->>Database: SELECT * FROM coordinate WHERE table_id = 1
        Database-->>SQLAlchemy: 坐标记录列表
        SQLAlchemy-->>CoordinateService: coordinates列表

        CoordinateService->>CoordinateService: 构建响应数据<br/>序列化Coordinate对象
        CoordinateService-->>FastAPI: {"coordinates": [...], "total": count}
        FastAPI-->>Client: HTTP 200 查询成功
    end

    %% 业务3：删除坐标数据
    rect rgb(255, 240, 240)
        Note over Client, Database: 🗑️ 业务3：删除坐标数据 (DELETE /coordinate/delete)

        Client->>FastAPI: DELETE /coordinate/delete?id=1<br/>删除表格1的所有坐标
        FastAPI->>CoordinateService: delete_coordinates_by_table(table_id=1)

        CoordinateService->>SQLAlchemy: query(Coordinate).filter(table_id==1).delete()
        SQLAlchemy->>Database: DELETE FROM coordinate WHERE table_id = 1
        Database-->>SQLAlchemy: 删除记录数量

        CoordinateService->>SQLAlchemy: commit() 提交事务
        SQLAlchemy->>Database: COMMIT TRANSACTION

        CoordinateService-->>FastAPI: {"message": "删除成功"}
        FastAPI-->>Client: HTTP 200 删除成功
    end

    %% 业务4：更新坐标信息
    rect rgb(240, 255, 240)
        Note over Client, Database: ✏️ 业务4：更新坐标信息 (PUT /coordinate/update)

        Client->>FastAPI: PUT /coordinate/update<br/>Body: {id:1, voc:"苹果", color:0, ...}
        FastAPI->>CoordinateService: update_coordinate(coordinate_id=1, data={...})

        CoordinateService->>SQLAlchemy: query(Coordinate).filter(id==1).first()
        SQLAlchemy->>Database: SELECT * FROM coordinate WHERE id = 1
        Database-->>SQLAlchemy: 目标坐标记录
        SQLAlchemy-->>CoordinateService: coordinate对象

        alt 坐标不存在
            CoordinateService-->>FastAPI: BusinessException("坐标不存在")
            FastAPI-->>Client: HTTP 400 业务错误
        end

        CoordinateService->>CoordinateService: 保存旧voc值<br/>更新坐标属性

        alt 有voc词汇内容
            CoordinateService->>SQLAlchemy: 查询相同voc+color的坐标数量<br/>COUNT(*) WHERE voc=voc AND color=color
            SQLAlchemy->>Database: SELECT COUNT(*) FROM coordinate WHERE voc = '苹果' AND color = 0
            Database-->>SQLAlchemy: 重复数量
            SQLAlchemy-->>CoordinateService: same_voc_count
            CoordinateService->>CoordinateService: 设置repeated = same_voc_count
        else 无voc内容
            CoordinateService->>CoordinateService: 设置repeated = 0
        end

        CoordinateService->>SQLAlchemy: commit() 提交更新
        SQLAlchemy->>Database: UPDATE coordinate SET ... WHERE id = 1

        alt voc发生变化且旧voc不为空
            CoordinateService->>CoordinateService: 更新相关坐标的repeated值<br/>_update_repeated_values_after_voc_change()
        end

        CoordinateService-->>FastAPI: {"coordinates": [updated_coordinate]}
        FastAPI-->>Client: HTTP 200 更新成功
    end

    %% 业务5：查询坐标关联词汇
    rect rgb(248, 240, 255)
        Note over Client, Database: 🔗 业务5：查询坐标关联词汇 (GET /coordinate/list)

        Client->>FastAPI: GET /coordinate/list?color=0&table_id=1&coordinate_id=1<br/>查询坐标关联的词汇列表
        FastAPI->>CoordinateService: get_phrases_by_coordinate(color=0, table_id=1, coordinate_id=1)

        CoordinateService->>SQLAlchemy: 查询TextInfo<br/>WHERE table_id=1 AND color=0
        SQLAlchemy->>Database: SELECT * FROM text_info WHERE table_id = 1 AND color = 0
        Database-->>SQLAlchemy: TextInfo记录
        SQLAlchemy-->>CoordinateService: text_info对象

        alt TextInfo不存在
            CoordinateService-->>FastAPI: BusinessException("未找到对应的文本信息")
            FastAPI-->>Client: HTTP 400 业务错误
        end

        CoordinateService->>SQLAlchemy: 查询关联Phrase<br/>WHERE text_id=text_info.id
        SQLAlchemy->>Database: SELECT * FROM phrase WHERE text_id = 1
        Database-->>SQLAlchemy: Phrase记录列表
        SQLAlchemy-->>CoordinateService: phrase_list

        CoordinateService->>CoordinateService: 构建Phrase响应数据<br/>包含table_id字段
        CoordinateService-->>FastAPI: {"phrases": [...], "total": count}
        FastAPI-->>Client: HTTP 200 查询成功
    end

    %% 异常处理
    Note over CoordinateService, Database: 🛡️ 统一异常处理机制

    alt 文件读取异常
        FileSystem-->>CoordinateService: IOError
        CoordinateService-->>FastAPI: BusinessException("读取cor.txt文件失败")
        FastAPI-->>Client: HTTP 400 文件错误
    end

    alt 数据库操作异常
        SQLAlchemy->>SQLAlchemy: rollback() 回滚事务
        SQLAlchemy->>Database: ROLLBACK TRANSACTION
        CoordinateService-->>FastAPI: BusinessException(错误信息)
        FastAPI-->>Client: HTTP 400 数据库错误
    end

    Note over Client, Database: 🎯 关键技术特性：<br/>• 文件解析与正则匹配<br/>• 分批插入优化性能<br/>• 重复计数自动维护<br/>• 雪花算法唯一ID<br/>• 跨表关联查询
                </div>
            </div>

            <div class="business-highlights">
                <div class="business-item">
                    <h4>📁 文件处理特性</h4>
                    <p>UTF-8编码读取、正则表达式解析、错误处理机制</p>
                </div>
                <div class="business-item">
                    <h4>🔄 批量操作特性</h4>
                    <p>分批插入、性能优化、进度控制、内存管理</p>
                </div>
                <div class="business-item">
                    <h4>🔍 查询优化特性</h4>
                    <p>索引优化、关联查询、数据序列化、响应构建</p>
                </div>
                <div class="business-item">
                    <h4>✏️ 更新机制特性</h4>
                    <p>重复计数维护、级联更新、事务一致性</p>
                </div>
                <div class="business-item">
                    <h4>🔗 关联查询特性</h4>
                    <p>跨表关联、数据聚合、业务逻辑封装</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📚 Cube FastAPI Coordinate 业务时序图 v6.0 | 🕒 生成时间: 2025-07-29</p>
            <p>🔧 技术栈: FastAPI + SQLAlchemy + 文件解析 + 正则表达式 | 🎯 特性: 批量导入 + 重复计数 + 关联查询</p>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 60,
                width: 160,
                height: 70,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 40,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });
    </script>
</body>
</html>
