删除词汇操作架构图 - v5.0 信息分层架构 - 纯文本内容
================================================================

v5.0 信息分层架构说明：
v5.0版本采用全新的信息分层架构，按照认知层次组织信息，实现信息饱满度与视觉简洁性的完美平衡

信息分层架构说明：
🎯 业务概念层 (Business Concept Layer) - 纯中文业务描述和流程概念
⚙️ 技术架构层 (Technical Architecture Layer) - 系统组件和设计模式
💻 实现细节层 (Implementation Detail Layer) - 完整代码和算法实现
📊 执行状态层 (Runtime State Layer) - 运行时数据和状态信息

v5.0 架构特点：
- 信息分层：按认知层次组织，从概念到实现到执行
- 布局优化：水平流动，减少连接线交叉
- 信息饱满：14个详细节点，覆盖完整技术链路
- 连接简洁：最少必要连接，突出核心数据流

================================================================
🎯 业务概念层 (Business Concept Layer)
================================================================

📋 删除词汇业务流程
业务目标：从文本中删除指定词汇，并保持全局编号的连续性
核心规则：
- 删除重复词汇时，优先删除编号较大的词汇
- 删除后，后续同词汇的编号需要前移补齐空缺
- 所有相关文本需要同步更新，保持全局一致性
- 操作需要支持事务回滚，确保数据完整性

🔄 业务处理步骤
1. 文本差异识别：比较修改前后的文本，找出被删除的词汇块
2. 删除策略判断：根据词汇是否带编号选择简单删除或复杂重编号
3. 编号重新分配：为剩余的同词汇重新分配连续编号
4. 全局文本同步：更新所有包含该词汇的文本记录
5. 结果验证确认：确保操作完成后数据的一致性和完整性

🏭 词汇服务初始化业务概念
业务含义：创建词汇管理服务实例，传入数据库会话进行初始化
调用方式：service = PhraseService(db)，然后调用service.delete_phrase(color, text)
业务价值：封装词汇管理的业务逻辑，提供统一的服务接口

📞 文本处理服务调用业务概念
业务含义：调用文本处理工具进行文本切割和差异检测
处理流程：split_text_by_comma(old_text) → split_text_by_comma(new_text) → find_different_blocks()
业务价值：获取需要删除的词汇块，为后续删除策略提供数据基础

🎯 删除策略选择业务概念
业务含义：遍历差异块，通过正则表达式模式匹配判断词汇是否带有数字后缀
策略分支：if match: 复杂删除重编号 else: 简单删除
业务价值：根据词汇特征选择最适合的删除处理方式

🔄 重编号业务逻辑概念
业务含义：查询需要重编号的词汇列表，遍历每个词汇计算新编号
处理逻辑：original_type → new_type = original_type - 1 → 构建映射关系
业务价值：确保全局编号连续性，维护数据的逻辑一致性

📝 全局文本同步逻辑概念
业务含义：收集所有受影响的文本记录，应用字符串替换规则更新文本内容
同步范围：所有包含目标词汇的TextInfo记录都需要同步更新
业务价值：保证全局数据一致性，避免数据孤岛问题

📊 业务场景示例
场景：用户从"苹果、苹果2、苹果3、香蕉"中删除"苹果2"
期望结果："苹果、苹果2、香蕉"（原苹果3变成苹果2）
影响范围：所有包含"苹果3"的文本都需要更新为"苹果2"

================================================================
⚙️ 技术架构层 (Technical Architecture Layer)
================================================================

🏗️ 系统架构组件
接口层：FastAPI路由器 + Pydantic数据验证
业务层：PhraseService词汇服务 + 业务规则处理
工具层：TextProcessor文本处理器 + 算法实现
数据层：SQLAlchemy ORM + 数据库事务管理
配置层：数据库连接池 + 依赖注入容器

🌐 HTTP接口层技术架构
前端请求：DELETE /phrase/delete，请求头application/json，请求体包含color和text字段
FastAPI路由器：@router.delete('/delete')装饰器，参数解析text_info: TextInfoColorUpdate
依赖注入：db: Session = Depends(get_db)，自动管理数据库会话生命周期
技术特点：支持自动API文档生成，类型安全的参数验证

🔧 配置管理层技术架构
数据库配置：app.config.database.py，DATABASE_URL配置，engine = create_engine(DATABASE_URL)
会话工厂：SessionLocal = sessionmaker(engine)，创建数据库会话工厂
会话管理：get_db()生成器函数，yield db返回会话，finally确保db.close()
扩展支持：支持Redis会话、缓存配置等组件集成

🔴 数据验证解析层技术架构
Pydantic验证：TextInfoColorUpdate验证模型，color字段范围0≤x≤8，text字段长度≤1000字符
类型转换：JSON自动转换为Python对象，支持必填字段存在性检查
验证结果：TextInfoColorUpdate(color=0, text='苹果、香蕉、橙子、')
技术优势：基于Python类型注解，提供编译时类型检查

🟣 业务逻辑层技术架构
服务封装：PhraseService类封装词汇管理业务逻辑，通过依赖注入获取数据库会话
工具调用：TextProcessor.split_text_by_comma()和find_different_blocks()提供算法支持
策略模式：根据词汇特征选择删除策略，支持简单删除和复杂重编号两种模式
事务协调：统筹数据层操作执行，确保业务逻辑的原子性

🟡 工具算法层技术架构
文本切割：split('、') + enumerate遍历，支持尾部文本特殊处理
差异检测：Counter统计算法，计算source_count - target_count差异
算法特点：独立于业务逻辑，支持单元测试，可复用性强
返回结果：TextSplitResult(blocks, tail_text)和differences列表

🟠 数据持久化层技术架构
ORM映射：SQLAlchemy提供对象关系映射，支持Phrase和TextInfo模型
SQL操作：DELETE删除、SELECT查询、UPDATE批量更新，支持条件查询和排序
事务管理：BEGIN TRANSACTION → 执行操作 → COMMIT/ROLLBACK
性能优化：批量更新操作，精确的WHERE条件，合理的索引使用

🟢 响应构建层技术架构
数据整理：收集业务层处理结果，格式化更新后的文本信息
响应构建：构建标准JSON响应结构，包含message和updated_text_infos
JSON序列化：JSONResponse自动序列化，大整数ID转换为字符串避免精度丢失
连接管理：依赖注入自动关闭数据库连接，确保资源正确释放

🔄 数据流设计模式
请求处理模式：HTTP请求 → 数据验证 → 业务处理 → 数据持久化 → 响应构建
依赖注入模式：数据库会话通过FastAPI的Depends机制自动管理
事务管理模式：业务操作包装在数据库事务中，支持自动回滚
错误处理模式：分层异常处理，业务异常与技术异常分离

📦 核心技术选型
Web框架：FastAPI - 高性能异步框架，自动API文档生成
数据验证：Pydantic - 基于Python类型注解的数据验证
ORM框架：SQLAlchemy - 成熟的Python ORM，支持复杂查询
数据库：关系型数据库，支持ACID事务特性

================================================================
💻 实现细节层 (Implementation Detail Layer)
================================================================

🔧 I1: FastAPI路由实现
@router.delete('/delete')
async def delete_phrase(
  text_info: TextInfoColorUpdate,
  db: Session = Depends(get_db)
):
  service = PhraseService(db)
  result = await service.delete_phrase(
    text_info.color, text_info.text
  )
  return result

✂️ I2: 文本切割算法详细实现
TextProcessor.split_text_by_comma():
parts = text.split('、')
for i, part in enumerate(parts):
  if i == len(parts) - 1:
    if text.endswith('、'):
      blocks.append(part) if part
      tail_text = ''
    else: tail_text = part
  else: blocks.append(part) if part
返回: TextSplitResult(blocks, tail_text)

🔍 I3: 差异检测算法详细实现
TextProcessor.find_different_blocks():
source_count = Counter(old_blocks)
target_count = Counter(new_blocks)
differences = []
for block, source_num in source_count.items():
  target_num = target_count.get(block, 0)
  diff = source_num - target_num
  if diff > 0:
    differences.extend([block] * diff)
返回: differences列表

🎯 I4: 删除策略判断实现
deleted_block = deleted_blocks[0]
has_digit_suffix = bool(
  re.search(r'\d$', deleted_block)
)
if not has_digit_suffix:
  _handle_delete_without_digit()
else:
  _handle_delete_with_digit()
正则模式: r'^(.+?)(\d+)$'

🗑️ I5: 简单删除实现
_handle_delete_without_digit():
self._delete_phrase_by_word_and_type(
  deleted_block, 0, text_info_id
)
self._db.query(TextInfo)
  .filter(TextInfo.id == text_info_id)
  .update({'text': new_text})
self._db.flush()

🔄 I6: 复杂删除重编号实现
_handle_delete_with_digit():
match = re.search(r'^(.+?)(\d+)$', deleted_block)
word = match.group(1)
type_value = int(match.group(2))
删除目标记录 + 查询需要重编号的phrase
批量更新type和text字段
事务提交

🗃️ I7: 数据库操作SQL详细实现
删除操作:
DELETE FROM phrase WHERE
  word=? AND type=? AND text_id=?
查询操作:
SELECT id,word,type,text_id FROM phrase
  WHERE word=? AND type>? ORDER BY type ASC
批量更新:
UPDATE phrase SET type=? WHERE id=?
UPDATE text_info SET text=? WHERE id=?

💾 I8: 事务管理详细实现
try:
  delete_result = 删除操作
  update_result = 更新操作
  self._db.commit()
  return result
except Exception as e:
  self._db.rollback()
  logger.error(f'Error: {str(e)}')
  raise BusinessException(f'删除失败：{str(e)}')

================================================================
📊 执行状态层 (Runtime State Layer)
================================================================

📥 R1: HTTP请求数据
POST /phrase/delete
Headers: {'Content-Type': 'application/json'}
Body: {
  'color': 0,
  'text': '苹果、香蕉、橙子、'
}
✅ Pydantic验证通过
TextInfoColorUpdate(color=0, text='苹果、香蕉、橙子、')

🔄 R2: 文本处理过程数据
old_text: '苹果、苹果2、香蕉、橙子、'
old_blocks: ['苹果', '苹果2', '香蕉', '橙子']
new_blocks: ['苹果', '香蕉', '橙子']
differences: ['苹果2']
has_digit_suffix: True
match.group(1): '苹果'
match.group(2): '2'

💾 R3: 数据库查询执行状态
删除操作: DELETE FROM phrase
  WHERE word='苹果' AND type=2 AND text_id=123
  affected_rows = 1
重编号查询: SELECT * FROM phrase
  WHERE word='苹果' AND type>2 ORDER BY type
  result_count = 2 (苹果3, 苹果4)
批量更新: 2条phrase记录, 2条text_info记录

🔄 R4: 重编号处理状态
phrase_list_to_update: [
  Phrase(word='苹果', type=3, text_id=456),
  Phrase(word='苹果', type=4, text_id=789)
]
更新映射: {
  '苹果3' -> '苹果2',
  '苹果4' -> '苹果3'
}
text_info更新: 2条记录文本替换

📤 R5: HTTP响应数据
Status: 200 OK
Headers: {'Content-Type': 'application/json'}
Body: {
  'message': '删除成功',
  'updated_text_infos': [
    {'id': '123', 'color': 0, 'text': '苹果、香蕉、橙子、'},
    {'id': '456', 'color': 1, 'text': '水果：苹果、苹果2'},
    {'id': '789', 'color': 2, 'text': '苹果3变成苹果2'}
  ]
}

⚠️ R6: 错误处理状态
try-catch包装:
BusinessException: 业务逻辑错误
HTTPException: HTTP状态码400
Database rollback: 事务回滚
Logger记录: error级别日志
响应格式: {'detail': '删除失败：具体错误信息'}

================================================================
v5.0 数据流连接关系
================================================================

主要数据流（简洁的水平流动）：
B1 --> T1 --> I1 --> R1: 业务概念 → 技术架构 → 路由实现 → HTTP请求
B2 --> T2 --> I4 --> R2: 业务步骤 → 设计模式 → 策略判断 → 文本处理

实现细节层内部流程（垂直流动，减少交叉）：
I1 --> I2: FastAPI路由 → 文本切割算法
I2 --> I3: 文本切割 → 差异检测算法
I3 --> I4: 差异检测 → 删除策略判断
I4 --> I5: 策略判断 → 简单删除（分支1）
I4 --> I6: 策略判断 → 复杂删除重编号（分支2）
I5 --> I7: 简单删除 → 数据库操作
I6 --> I7: 复杂删除 → 数据库操作
I7 --> I8: 数据库操作 → 事务管理

执行状态层内部流程（垂直流动）：
R1 --> R2: HTTP请求 → 文本处理过程
R2 --> R3: 文本处理 → 数据库执行状态
R3 --> R4: 数据库执行 → 重编号处理状态
R4 --> R5: 重编号处理 → HTTP响应

最少必要的跨层连接：
I8 --> R5: 事务处理 → 最终响应
R6 -.-> R5: 异常处理 → 响应（虚线表示异常情况）

================================================================
v5.0 信息分层架构特点总结
================================================================

核心创新：
- 信息分层: 按认知层次组织，从概念到实现到执行
- 认知优化: 减少信息混合造成的认知负荷
- 布局革新: 水平流动布局，减少连接线交叉
- 信息密度: 14个详细节点，保持信息饱满度

信息分层架构优势：
🎯 业务概念层: 纯中文描述，业务人员易理解
⚙️ 技术架构层: 系统组件清晰，架构师易把握
💻 实现细节层: 完整代码实现，开发人员易实现
📊 执行状态层: 运行时数据，运维人员易监控

布局优化成果：
- 水平流动: 从左到右的自然阅读顺序
- 连接简洁: 从20+连接优化到12个核心连接
- 视觉清晰: 减少连接线交叉，提升可读性
- 空间利用: 紧凑布局，信息密度高

v5.0 vs v4.0 对比：
v4.0: 技术层次分离，职责明确
v5.0: 信息类型分离，认知优化

v4.0: 垂直架构图，连接复杂
v5.0: 水平流动图，连接简洁

v4.0: 中英文混合，认知负荷高
v5.0: 信息分层，认知负荷低

实际应用价值：
- 认知友好: 按信息类型分层，降低理解难度
- 视觉优化: 水平布局，减少视觉混乱
- 信息完整: 保持技术细节的完整性
- 架构清晰: 突出核心数据流和关键节点
