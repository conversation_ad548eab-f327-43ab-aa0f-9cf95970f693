<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Phrase 时序图 v6.0 - Cube FastAPI</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 300;
        }
        
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e9ecef;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .mermaid {
            text-align: center;
            min-width: 1400px;
        }
        
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .info-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
            margin: 20px 0;
        }
        
        .tech-highlights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .tech-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .tech-item h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .footer {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 40px;
        }
        
        .version-badge {
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Delete Phrase 时序图 <span class="version-badge">v6.0</span></h1>
            <p>Cube FastAPI 词汇删除功能完整技术架构与数据流分析</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 重要说明：</strong>此时序图展示了delete_phrase功能从HTTP请求到数据库响应的完整流程，包括简单删除和复杂删除两种策略分支。图表内容较宽，建议使用横向滚动查看完整交互流程。
            </div>
            
            <div class="info-box">
                <h3>🎯 时序图核心特性</h3>
                <ul>
                    <li><strong>分层架构展示</strong>：从接口层到数据层的完整调用链路</li>
                    <li><strong>策略模式实现</strong>：根据词汇特征动态选择删除策略</li>
                    <li><strong>事务管理机制</strong>：确保数据一致性的事务控制流程</li>
                    <li><strong>异常处理流程</strong>：完整的错误处理和回滚机制</li>
                    <li><strong>性能优化点</strong>：批量操作和依赖注入的实现细节</li>
                </ul>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
sequenceDiagram
    participant Client as 🖥️ 前端客户端
    participant FastAPI as 🌐 FastAPI路由器
    participant Pydantic as 🔴 Pydantic验证器
    participant DB_Session as 🔧 数据库会话管理
    participant PhraseService as 🟣 PhraseService
    participant TextProcessor as 🟡 TextProcessor
    participant SQLAlchemy as 🟠 SQLAlchemy ORM
    participant Database as 🗄️ SQLite数据库

    Note over Client, Database: Delete Phrase 完整业务流程 v6.0

    %% 1. HTTP请求阶段
    Client->>FastAPI: DELETE /phrase/delete<br/>Headers: application/json<br/>Body: {color: 0, text: "香蕉、橙子、"}
    
    FastAPI->>Pydantic: 验证请求参数<br/>TextInfoColorUpdate模型
    Pydantic->>Pydantic: color字段验证(0≤x≤8)<br/>text字段验证(≤1000字符)
    
    alt 参数验证失败
        Pydantic-->>FastAPI: ValidationError
        FastAPI-->>Client: HTTP 422 参数错误
    else 参数验证成功
        Pydantic->>FastAPI: TextInfoColorUpdate(color=0, text="香蕉、橙子、")
    end

    %% 2. 依赖注入和会话管理
    FastAPI->>DB_Session: get_db() 依赖注入
    DB_Session->>DB_Session: SessionLocal() 创建会话
    DB_Session->>FastAPI: yield db_session

    %% 3. 业务服务调用
    FastAPI->>PhraseService: delete_phrase(color=0, new_text="香蕉、橙子、")
    
    %% 4. 获取当前文本信息
    PhraseService->>SQLAlchemy: query(TextInfo).filter(color==0).first()
    SQLAlchemy->>Database: SELECT * FROM text_info WHERE color = 0
    Database-->>SQLAlchemy: TextInfo记录
    SQLAlchemy-->>PhraseService: old_text_info对象

    alt TextInfo不存在
        PhraseService-->>FastAPI: BusinessException("文本信息不存在")
        FastAPI-->>Client: HTTP 400 业务错误
    end

    %% 5. 文本分析阶段
    Note over PhraseService, TextProcessor: 文本差异分析算法
    
    PhraseService->>TextProcessor: split_text_by_comma(old_text)<br/>old_text="苹果、香蕉、橙子、"
    TextProcessor->>TextProcessor: text.split("、") + 尾部处理
    TextProcessor-->>PhraseService: TextSplitResult(blocks=["苹果","香蕉","橙子"], tail_text="")

    PhraseService->>TextProcessor: split_text_by_comma(new_text)<br/>new_text="香蕉、橙子、"
    TextProcessor->>TextProcessor: text.split("、") + 尾部处理
    TextProcessor-->>PhraseService: TextSplitResult(blocks=["   香蕉","橙子"], tail_text="")

    PhraseService->>TextProcessor: find_different_blocks(old_blocks, new_blocks)    
    TextProcessor->>TextProcessor: Counter算法计算差异<br/>source_count - target_count
    TextProcessor-->>PhraseService: deleted_blocks=["苹果"]

    alt 无差异词汇
        PhraseService-->>FastAPI: {"message": "没有需要删除的词汇"}
        FastAPI-->>Client: HTTP 200 无操作
    end

    %% 6. 删除策略选择
    Note over PhraseService: 策略模式：根据词汇特征选择删除策略

    PhraseService->>PhraseService: 检查deleted_block是否有数字后缀<br/>regex: r'\d$'

    alt 简单删除分支（无数字后缀）
        Note over PhraseService, Database: 简单删除流程：直接删除+更新文本

        PhraseService->>SQLAlchemy: DELETE FROM phrase<br/>WHERE word="苹果" AND type=0 AND text_id=text_info_id
        SQLAlchemy->>Database: DELETE FROM phrase WHERE word = '苹果' AND type = 0 AND text_id = 1
        Database-->>SQLAlchemy: 删除结果

        PhraseService->>SQLAlchemy: UPDATE text_info SET text=new_text<br/>WHERE id=text_info_id
        SQLAlchemy->>Database: UPDATE text_info SET text = '香蕉、橙子、' WHERE id = 1
        Database-->>SQLAlchemy: 更新结果

        PhraseService->>SQLAlchemy: commit() 提交事务
        SQLAlchemy->>Database: COMMIT TRANSACTION

        PhraseService-->>FastAPI: {"message": "删除成功"}

    else 复杂删除分支（有数字后缀）
        Note over PhraseService, Database: 复杂删除流程：删除+重编号+批量更新

        PhraseService->>PhraseService: 解析词汇和数字后缀<br/>regex: r'^(.+?)(\d+)$'<br/>例：苹果1 → word="苹果", type=1

        %% 删除目标词汇
        PhraseService->>SQLAlchemy: DELETE FROM phrase<br/>WHERE word=word AND type=type_value AND text_id=text_info_id
        SQLAlchemy->>Database: DELETE FROM phrase WHERE word = '苹果' AND type = 1 AND text_id = 1
        Database-->>SQLAlchemy: 删除结果

        %% 更新当前TextInfo
        PhraseService->>SQLAlchemy: UPDATE text_info SET text=new_text<br/>WHERE id=text_info_id
        SQLAlchemy->>Database: UPDATE text_info SET text = '香蕉、橙子、' WHERE id = 1
        Database-->>SQLAlchemy: 更新结果

        PhraseService->>SQLAlchemy: flush() 立即生效

        %% 查找需要重编号的相关词汇
        PhraseService->>SQLAlchemy: SELECT * FROM phrase<br/>WHERE word=word AND type>type_value<br/>ORDER BY type ASC
        SQLAlchemy->>Database: SELECT * FROM phrase WHERE word = '苹果' AND type > 1 ORDER BY type ASC
        Database-->>SQLAlchemy: 相关词汇列表（按type升序）

        alt 有相关词汇需要重编号
            Note over PhraseService, Database: 批量重编号处理

            %% 获取相关TextInfo对象
            PhraseService->>SQLAlchemy: SELECT * FROM text_info<br/>WHERE id IN (text_ids)
            SQLAlchemy->>Database: SELECT * FROM text_info WHERE id IN (2,3,4)
            Database-->>SQLAlchemy: 相关TextInfo对象列表

            %% 批量更新词汇类型和文本内容
            loop 遍历每个需要更新的phrase
                PhraseService->>PhraseService: 计算新type值（减1）<br/>构建新词汇文本
                PhraseService->>PhraseService: 在对应TextInfo中替换词汇文本<br/>例：苹果2 → 苹果1
                PhraseService->>PhraseService: 更新phrase.type值
            end

            PhraseService->>SQLAlchemy: bulk_update_mappings(Phrase, phrase_updates)
            SQLAlchemy->>Database: 批量UPDATE phrase表

            PhraseService->>SQLAlchemy: bulk_update_mappings(TextInfo, text_updates)
            SQLAlchemy->>Database: 批量UPDATE text_info表

            PhraseService->>SQLAlchemy: commit() 提交事务
            SQLAlchemy->>Database: COMMIT TRANSACTION

            %% 收集所有更新的TextInfo
            PhraseService->>SQLAlchemy: 查询当前和相关的TextInfo对象
            SQLAlchemy->>Database: SELECT查询更新后的数据
            Database-->>SQLAlchemy: 完整的TextInfo对象列表

            PhraseService-->>FastAPI: {"message": "删除成功",<br/>"updated_text_infos": [TextInfo列表]}

        else 无相关词汇
            PhraseService->>SQLAlchemy: commit() 提交事务
            SQLAlchemy->>Database: COMMIT TRANSACTION

            PhraseService-->>FastAPI: {"message": "删除成功",<br/>"updated_text_infos": [当前TextInfo]}
        end
    end

    %% 7. 响应构建和资源清理
    FastAPI->>FastAPI: 构建JSON响应<br/>序列化TextInfo对象

    FastAPI->>DB_Session: 会话生命周期结束
    DB_Session->>DB_Session: db.close() 关闭连接

    FastAPI-->>Client: HTTP 200 成功响应<br/>Content-Type: application/json

    %% 8. 异常处理流程
    Note over PhraseService, Database: 异常处理机制

    alt 业务异常或数据库错误
        SQLAlchemy->>SQLAlchemy: rollback() 回滚事务
        SQLAlchemy->>Database: ROLLBACK TRANSACTION
        PhraseService-->>FastAPI: BusinessException(错误信息)
        FastAPI-->>Client: HTTP 400 业务错误
    end

    Note over Client, Database: 🎯 关键技术特性：<br/>• 策略模式选择删除方式<br/>• 事务保证数据一致性<br/>• 批量操作提升性能<br/>• 依赖注入管理资源<br/>• 分层异常处理机制
                </div>
            </div>  

            <div class="tech-highlights">
                <div class="tech-item">
                    <h4>🌐 接口层特性</h4>
                    <p>FastAPI自动参数验证、依赖注入、异常处理</p>
                </div>
                <div class="tech-item">
                    <h4>🟣 业务层特性</h4>
                    <p>策略模式、事务协调、复杂业务逻辑处理</p>
                </div>
                <div class="tech-item">
                    <h4>🟡 工具层特性</h4>
                    <p>Counter算法、正则表达式、文本分析</p>
                </div>
                <div class="tech-item">
                    <h4>🟠 数据层特性</h4>
                    <p>批量操作、事务管理、ORM映射</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📚 Cube FastAPI Delete Phrase 时序图 v6.0 | 🕒 生成时间: 2025-07-29</p>
            <p>🔧 技术栈: FastAPI + SQLAlchemy + Pydantic + SQLite | 🎯 架构: 分层架构 + 策略模式</p>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 60,
                width: 160,
                height: 70,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 40,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });
    </script>
</body>
</html>
