<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Phrase 时序图 v6.0 - Cube FastAPI</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 300;
        }
        
        .header p {
            margin: 15px 0 0 0;
            opacity: 0.9;
            font-size: 1.2em;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #e9ecef;
            overflow-x: auto;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .mermaid {
            text-align: center;
            min-width: 1400px;
        }
        
        .warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #ffc107;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .info-box {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #28a745;
            margin: 20px 0;
        }
        
        .tech-highlights {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .tech-item {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .tech-item h4 {
            color: #2c3e50;
            margin-top: 0;
            margin-bottom: 10px;
        }
        
        .footer {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            text-align: center;
            padding: 30px;
            margin-top: 40px;
        }
        
        .version-badge {
            background: #e74c3c;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
            display: inline-block;
            margin-left: 10px;
        }
        
        .algorithm-box {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            color: #1565c0;
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid #2196f3;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>➕ Add Phrase 时序图 <span class="version-badge">v6.0</span></h1>
            <p>Cube FastAPI 智能词汇添加功能完整技术架构与数据流分析</p>
        </div>
        
        <div class="content">
            <div class="warning">
                <strong>⚠️ 重要说明：</strong>此时序图展示了add_phrase功能从HTTP请求到数据库响应的完整流程，包括智能词汇识别、重复编号处理和批量数据操作。图表内容较宽，建议使用横向滚动查看完整交互流程。
            </div>
            
            <div class="info-box">
                <h3>🎯 Add Phrase 核心特性</h3>
                <ul>
                    <li><strong>智能差异识别</strong>：通过Counter算法精确识别新增词汇</li>
                    <li><strong>自动重复编号</strong>：为重复词汇自动添加数字后缀（如：苹果→苹果1）</li>
                    <li><strong>索引映射优化</strong>：优先选择后面出现的相同文本块作为新增</li>
                    <li><strong>批量数据操作</strong>：高效的批量插入和更新机制</li>
                    <li><strong>事务一致性</strong>：完整的事务管理和异常回滚机制</li>
                </ul>
            </div>

            <div class="algorithm-box">
                <h3>🧮 核心算法说明</h3>
                <p><strong>差异检测算法：</strong>使用Counter统计新旧文本块差异，计算 new_count - old_count 得到新增词汇</p>
                <p><strong>索引映射算法：</strong>为每个新增词汇分配在文本中的位置索引，优先选择后面出现的相同词汇</p>
                <p><strong>重复编号算法：</strong>查询全局词汇表统计相同词汇数量，为新增词汇分配type值（重复次数）</p>
            </div>

            <div class="diagram-container">
                <div class="mermaid">
sequenceDiagram
    participant Client as 🖥️ 前端客户端
    participant FastAPI as 🌐 FastAPI路由器
    participant Pydantic as 🔴 Pydantic验证器
    participant DB_Session as 🔧 数据库会话管理
    participant PhraseService as 🟣 PhraseService
    participant TextProcessor as 🟡 TextProcessor
    participant IdGenerator as 🆔 IdGenerator
    participant SQLAlchemy as 🟠 SQLAlchemy ORM
    participant Database as 🗄️ SQLite数据库

    Note over Client, Database: Add Phrase 智能词汇添加流程 v6.0

    %% 1. HTTP请求阶段
    Client->>FastAPI: POST /phrase/add<br/>Headers: application/json<br/>Body: {color: 0, text: "苹果、香蕉、橙子、苹果、"}
    
    FastAPI->>Pydantic: 验证请求参数<br/>TextInfoColorUpdate模型
    Pydantic->>Pydantic: color字段验证(0≤x≤8)<br/>text字段验证(≤1000字符)
    
    alt 参数验证失败
        Pydantic-->>FastAPI: ValidationError
        FastAPI-->>Client: HTTP 422 参数错误
    else 参数验证成功
        Pydantic->>FastAPI: TextInfoColorUpdate(color=0, text="苹果、香蕉、橙子、苹果、")
    end

    %% 2. 依赖注入和会话管理
    FastAPI->>DB_Session: get_db() 依赖注入
    DB_Session->>DB_Session: SessionLocal() 创建会话
    DB_Session->>FastAPI: yield db_session

    %% 3. 业务服务调用
    FastAPI->>PhraseService: add_phrase(color=0, new_text="苹果、香蕉、橙子、苹果、")
    
    %% 4. 获取当前文本信息
    PhraseService->>SQLAlchemy: query(TextInfo).filter(color==0).first()
    SQLAlchemy->>Database: SELECT * FROM text_info WHERE color = 0
    Database-->>SQLAlchemy: TextInfo记录
    SQLAlchemy-->>PhraseService: old_text_info对象

    alt TextInfo不存在
        PhraseService-->>FastAPI: BusinessException("文本信息不存在")
        FastAPI-->>Client: HTTP 400 业务错误
    end

    %% 5. 文本分析阶段
    Note over PhraseService, TextProcessor: 智能文本差异分析

    PhraseService->>TextProcessor: split_text_by_comma(old_text)<br/>old_text="苹果、香蕉、橙子、"
    TextProcessor->>TextProcessor: text.split("、") + 尾部处理
    TextProcessor-->>PhraseService: TextSplitResult(blocks=["苹果","香蕉","橙子"], tail_text="")

    PhraseService->>TextProcessor: split_text_by_comma(new_text)<br/>new_text="苹果、香蕉、橙子、苹果、"
    TextProcessor->>TextProcessor: text.split("、") + 尾部处理
    TextProcessor-->>PhraseService: TextSplitResult(blocks=["苹果","香蕉","橙子","苹果"], tail_text="")

    PhraseService->>TextProcessor: find_different_blocks(new_blocks, old_blocks)
    TextProcessor->>TextProcessor: Counter算法计算差异<br/>new_count - old_count
    TextProcessor-->>PhraseService: diff_phrase_list=["苹果"]

    alt 无新增词汇
        PhraseService-->>FastAPI: {"message": "没有新增词汇", "text_info": current_text_info}
        FastAPI-->>Client: HTTP 200 无操作
    end

    %% 6. 索引映射计算
    Note over PhraseService, TextProcessor: 索引映射算法

    PhraseService->>TextProcessor: get_block_index_map(new_blocks, diff_phrase_list)
    TextProcessor->>TextProcessor: 优先选择后面出现的相同文本块<br/>构建索引映射：{"苹果_0": 3}
    TextProcessor-->>PhraseService: block_index_map={"苹果_0": 3}

    %% 7. 全局词汇查询
    PhraseService->>SQLAlchemy: query(Phrase).all()
    SQLAlchemy->>Database: SELECT * FROM phrase
    Database-->>SQLAlchemy: 全局词汇列表
    SQLAlchemy-->>PhraseService: all_phrases列表

    %% 8. 智能词汇处理和编号
    Note over PhraseService, IdGenerator: 智能词汇创建和重复编号

    loop 遍历每个新增词汇
        PhraseService->>PhraseService: 提取词汇内容word="苹果"
        PhraseService->>PhraseService: 统计相同词汇数量count=1<br/>（已存在1个"苹果"）

        %% 生成唯一ID
        PhraseService->>IdGenerator: generate_id()
        IdGenerator->>IdGenerator: 雪花算法生成唯一ID
        IdGenerator-->>PhraseService: unique_id

        %% 创建新Phrase对象
        PhraseService->>PhraseService: 创建Phrase对象<br/>id=unique_id, word="苹果", type=1

        %% 更新文本块编号
        PhraseService->>PhraseService: 在索引位置3添加数字后缀<br/>modified_blocks[3] = "苹果1"
    end

    %% 9. 批量数据操作
    Note over PhraseService, Database: 批量数据持久化

    PhraseService->>SQLAlchemy: 批量插入新词汇<br/>save_phrases_batch(new_phrases)
    SQLAlchemy->>Database: INSERT INTO phrase (id, text_id, word, type) VALUES (...)
    Database-->>SQLAlchemy: 插入结果

    %% 10. 文本重构和更新
    PhraseService->>PhraseService: 构建最终文本<br/>final_text = "苹果、香蕉、橙子、苹果1、"

    PhraseService->>SQLAlchemy: UPDATE text_info SET text=final_text<br/>WHERE id=text_info_id
    SQLAlchemy->>Database: UPDATE text_info SET text = '苹果、香蕉、橙子、苹果1、' WHERE id = 1
    Database-->>SQLAlchemy: 更新结果

    %% 11. 事务提交
    PhraseService->>SQLAlchemy: commit() 提交事务
    SQLAlchemy->>Database: COMMIT TRANSACTION

    %% 12. 响应构建
    PhraseService->>PhraseService: 构建响应对象<br/>包含更新后的TextInfo
    PhraseService-->>FastAPI: {"message": "添加成功", "text_info": updated_text_info}

    %% 13. 资源清理
    FastAPI->>FastAPI: 构建JSON响应<br/>序列化TextInfo对象

    FastAPI->>DB_Session: 会话生命周期结束
    DB_Session->>DB_Session: db.close() 关闭连接

    FastAPI-->>Client: HTTP 200 成功响应<br/>Content-Type: application/json

    %% 14. 异常处理流程
    Note over PhraseService, Database: 异常处理机制

    alt 业务异常或数据库错误
        SQLAlchemy->>SQLAlchemy: rollback() 回滚事务
        SQLAlchemy->>Database: ROLLBACK TRANSACTION
        PhraseService-->>FastAPI: BusinessException(错误信息)
        FastAPI-->>Client: HTTP 400 业务错误
    end

    Note over Client, Database: 🎯 关键技术特性：<br/>• Counter算法精确差异检测<br/>• 智能索引映射优化<br/>• 自动重复编号机制<br/>• 批量数据操作优化<br/>• 雪花算法唯一ID生成
                </div>
            </div>

            <div class="tech-highlights">
                <div class="tech-item">
                    <h4>🌐 接口层特性</h4>
                    <p>FastAPI自动参数验证、依赖注入、异常处理</p>
                </div>
                <div class="tech-item">
                    <h4>🟣 业务层特性</h4>
                    <p>智能词汇识别、重复编号、事务协调</p>
                </div>
                <div class="tech-item">
                    <h4>🟡 工具层特性</h4>
                    <p>Counter算法、索引映射、文本重构</p>
                </div>
                <div class="tech-item">
                    <h4>🆔 ID生成特性</h4>
                    <p>雪花算法、分布式唯一ID、线程安全</p>
                </div>
                <div class="tech-item">
                    <h4>🟠 数据层特性</h4>
                    <p>批量操作、事务管理、ORM映射</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>📚 Cube FastAPI Add Phrase 时序图 v6.0 | 🕒 生成时间: 2025-07-29</p>
            <p>🔧 技术栈: FastAPI + SQLAlchemy + Pydantic + SQLite | 🎯 算法: Counter差异检测 + 雪花算法ID生成</p>
        </div>
    </div>

    <script>
        // 初始化 Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 60,
                width: 160,
                height: 70,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 40,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            }
        });
    </script>
</body>
</html>
